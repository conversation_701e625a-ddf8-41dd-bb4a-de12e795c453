<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气密性图片页面初始化测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-container {
            margin: 20px;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        .test-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">气密性图片页面初始化测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="test-container">
                    <h3>测试控制</h3>
                    <button id="load-tab-btn" class="btn btn-primary me-2">模拟加载标签页</button>
                    <button id="clear-log-btn" class="btn btn-secondary me-2">清空日志</button>
                    <button id="reset-test-btn" class="btn btn-warning">重置测试</button>
                    
                    <div class="test-log" id="test-log">
                        <div class="info">等待测试开始...</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="test-container">
                    <h3>标签页内容容器</h3>
                    <div id="tab-content-container" style="min-height: 400px; border: 1px dashed #ccc; padding: 10px;">
                        <div class="text-muted text-center">标签页内容将在这里加载</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/tab_initializer.js"></script>
    <script src="/static/js/tabs.js"></script>
    
    <script>
        // 测试日志记录
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            const logContainer = document.getElementById('test-log');
            logContainer.innerHTML = '<div class="info">日志已清空</div>';
        }

        // 重置测试
        function resetTest() {
            // 清理全局变量
            if (window.imageManager) {
                delete window.imageManager;
            }
            if (window.airtightnessScriptLoaded) {
                delete window.airtightnessScriptLoaded;
            }
            
            // 清空容器
            const container = document.getElementById('tab-content-container');
            container.innerHTML = '<div class="text-muted text-center">标签页内容将在这里加载</div>';
            
            logMessage('测试环境已重置', 'warning');
        }

        // 模拟标签页加载
        async function simulateTabLoad() {
            logMessage('开始模拟标签页加载...', 'info');
            
            try {
                // 1. 获取页面内容
                logMessage('正在获取页面内容...', 'info');
                const response = await fetch('/airtightness/images', {
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const content = await response.text();
                logMessage('页面内容获取成功', 'success');
                
                // 2. 插入内容到容器
                const container = document.getElementById('tab-content-container');
                container.innerHTML = content;
                logMessage('页面内容已插入到容器', 'success');
                
                // 3. 等待一小段时间模拟真实环境
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // 4. 检查DOM元素
                const vehicleSelect = container.querySelector('#vehicle-select');
                const viewImagesBtn = container.querySelector('#view-images-btn');
                const openNewWindowBtn = container.querySelector('#open-new-window-btn');
                const imagesContainer = container.querySelector('#images-container');
                const emptyState = container.querySelector('#empty-state');
                
                logMessage(`DOM元素检查:`, 'info');
                logMessage(`  vehicle-select: ${vehicleSelect ? '✓' : '✗'}`, vehicleSelect ? 'success' : 'error');
                logMessage(`  view-images-btn: ${viewImagesBtn ? '✓' : '✗'}`, viewImagesBtn ? 'success' : 'error');
                logMessage(`  open-new-window-btn: ${openNewWindowBtn ? '✓' : '✗'}`, openNewWindowBtn ? 'success' : 'error');
                logMessage(`  images-container: ${imagesContainer ? '✓' : '✗'}`, imagesContainer ? 'success' : 'error');
                logMessage(`  empty-state: ${emptyState ? '✓' : '✗'}`, emptyState ? 'success' : 'error');
                
                // 5. 触发初始化
                logMessage('开始触发初始化...', 'info');
                if (window.initTabContent) {
                    window.initTabContent(container);
                    logMessage('initTabContent 已调用', 'success');
                } else {
                    logMessage('initTabContent 函数不存在', 'error');
                }
                
                // 6. 等待初始化完成并检查结果
                setTimeout(() => {
                    const select = container.querySelector('#vehicle-select');
                    if (select && select.options.length > 1) {
                        logMessage(`车型下拉框初始化成功，共有 ${select.options.length - 1} 个选项`, 'success');
                        
                        // 列出所有选项
                        for (let i = 1; i < select.options.length; i++) {
                            logMessage(`  选项 ${i}: ${select.options[i].textContent}`, 'info');
                        }
                    } else {
                        logMessage('车型下拉框初始化失败或无选项', 'error');
                    }
                    
                    // 检查全局变量
                    if (window.imageManager) {
                        logMessage('window.imageManager 已创建', 'success');
                    } else {
                        logMessage('window.imageManager 未创建', 'error');
                    }
                }, 2000);
                
            } catch (error) {
                logMessage(`加载失败: ${error.message}`, 'error');
            }
        }

        // 绑定事件
        document.getElementById('load-tab-btn').addEventListener('click', simulateTabLoad);
        document.getElementById('clear-log-btn').addEventListener('click', clearLog);
        document.getElementById('reset-test-btn').addEventListener('click', resetTest);

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('测试页面加载完成', 'success');
        });
    </script>
</body>
</html>
