/**
 * 气密性模块JavaScript
 */

// 全局变量
let selectedVehicles = [];
let allVehicles = [];

// 工具函数
const utils = {
    // 显示消息
    showMessage: function(message, type = 'info') {
        // 创建消息元素
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    },
    
    // 发送请求
    request: async function(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.code !== 200) {
                throw new Error(data.message || '请求失败');
            }
            
            return data;
        } catch (error) {
            console.error('Request failed:', error);
            throw error;
        }
    }
};

// 多选框组件
class VehicleMultiSelect {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.selectedVehicles = [];
        this.allVehicles = [];
        this.isOpen = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadVehicles();
    }
    
    setupEventListeners() {
        const inputContainer = this.container.querySelector('.multiselect-input-container');
        const searchInput = this.container.querySelector('.multiselect-search input');
        
        // 点击输入框切换下拉菜单
        inputContainer.addEventListener('click', () => {
            this.toggle();
        });
        
        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            this.filterOptions(e.target.value);
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        });
    }
    
    async loadVehicles() {
        try {
            const result = await utils.request('/airtightness/api/vehicles');
            this.allVehicles = result.data;
            this.renderOptions();
        } catch (error) {
            utils.showMessage('加载车型列表失败: ' + error.message, 'error');
        }
    }
    
    renderOptions() {
        const optionsContainer = this.container.querySelector('.multiselect-options');
        optionsContainer.innerHTML = '';
        
        this.allVehicles.forEach(vehicle => {
            const option = document.createElement('div');
            option.className = 'multiselect-option';
            option.innerHTML = `
                <input type="checkbox" id="vehicle-${vehicle.id}" ${this.isSelected(vehicle.id) ? 'checked' : ''}>
                <label for="vehicle-${vehicle.id}">${vehicle.name}</label>
            `;
            
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const checkbox = option.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
                this.toggleVehicle(vehicle);
            });
            
            optionsContainer.appendChild(option);
        });
    }
    
    filterOptions(searchTerm) {
        const options = this.container.querySelectorAll('.multiselect-option');
        options.forEach(option => {
            const label = option.querySelector('label').textContent.toLowerCase();
            const isVisible = label.includes(searchTerm.toLowerCase());
            option.style.display = isVisible ? 'flex' : 'none';
        });
    }
    
    toggleVehicle(vehicle) {
        const index = this.selectedVehicles.findIndex(v => v.id === vehicle.id);
        if (index > -1) {
            this.selectedVehicles.splice(index, 1);
        } else {
            this.selectedVehicles.push(vehicle);
        }
        
        this.updateDisplay();
        this.updateSelectedOptions();
        this.onSelectionChange();
    }
    
    isSelected(vehicleId) {
        return this.selectedVehicles.some(v => v.id === vehicleId);
    }
    
    updateDisplay() {
        const input = this.container.querySelector('.multiselect-input');
        const selectedItemsContainer = this.container.querySelector('.selected-items');
        
        if (this.selectedVehicles.length === 0) {
            input.value = '';
            input.placeholder = '点击选择车型...';
            selectedItemsContainer.innerHTML = '';
        } else {
            input.value = `已选择 ${this.selectedVehicles.length} 个车型`;
            input.placeholder = '';
            
            // 渲染选中的车型标签
            selectedItemsContainer.innerHTML = this.selectedVehicles.map(vehicle => `
                <span class="selected-item">
                    ${vehicle.name}
                    <span class="remove-btn" data-vehicle-id="${vehicle.id}">×</span>
                </span>
            `).join('');
            
            // 绑定删除事件
            selectedItemsContainer.querySelectorAll('.remove-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const vehicleId = parseInt(btn.dataset.vehicleId);
                    const vehicle = this.selectedVehicles.find(v => v.id === vehicleId);
                    if (vehicle) {
                        this.toggleVehicle(vehicle);
                    }
                });
            });
        }
    }
    
    updateSelectedOptions() {
        const options = this.container.querySelectorAll('.multiselect-option');
        options.forEach(option => {
            const checkbox = option.querySelector('input[type="checkbox"]');
            const vehicleId = parseInt(checkbox.id.replace('vehicle-', ''));
            const isSelected = this.isSelected(vehicleId);
            
            checkbox.checked = isSelected;
            option.classList.toggle('selected', isSelected);
        });
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        this.container.querySelector('.multiselect-container').classList.add('open');
        this.isOpen = true;
        
        // 清空搜索框
        const searchInput = this.container.querySelector('.multiselect-search input');
        searchInput.value = '';
        this.filterOptions('');
        
        // 聚焦搜索框
        setTimeout(() => searchInput.focus(), 100);
    }
    
    close() {
        this.container.querySelector('.multiselect-container').classList.remove('open');
        this.isOpen = false;
    }
    
    getSelectedVehicleIds() {
        return this.selectedVehicles.map(v => v.id);
    }
    
    onSelectionChange() {
        // 子类可以重写此方法
    }
}

// 泄漏量对比管理器
class AirtightnessComparisonManager {
    constructor() {
        this.multiSelect = null;
        this.init();
    }

    init() {
        // 初始化多选框
        this.multiSelect = new VehicleMultiSelect('vehicle-multiselect');
        this.multiSelect.onSelectionChange = () => {
            this.updateGenerateButton();
        };

        this.setupEventListeners();
    }

    setupEventListeners() {
        // 生成对比表按钮
        document.getElementById('generate-comparison-btn').addEventListener('click', () => {
            this.generateComparison();
        });

        // 导出按钮
        document.getElementById('export-btn').addEventListener('click', () => {
            this.exportData();
        });
    }

    updateGenerateButton() {
        const btn = document.getElementById('generate-comparison-btn');
        const hasSelection = this.multiSelect.getSelectedVehicleIds().length > 0;
        btn.disabled = !hasSelection;
    }

    async generateComparison() {
        const vehicleIds = this.multiSelect.getSelectedVehicleIds();
        if (vehicleIds.length === 0) {
            utils.showMessage('请选择至少一个车型', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const result = await utils.request('/airtightness/api/comparison', {
                method: 'POST',
                body: JSON.stringify({ vehicle_ids: vehicleIds })
            });

            this.renderComparisonTable(result.data);
            this.showResults(true);
            utils.showMessage('对比数据生成成功', 'success');
        } catch (error) {
            utils.showMessage('生成对比数据失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    renderComparisonTable(data) {
        const table = document.getElementById('comparison-table');
        const testInfoTable = document.getElementById('test-info-table');

        // 渲染主对比表
        this.renderMainTable(table, data);

        // 渲染测试信息表
        this.renderTestInfoTable(testInfoTable, data);

        // 更新车型数量
        document.getElementById('vehicle-count').textContent = `${data.vehicles.length} 个车型`;
    }

    renderMainTable(table, data) {
        const thead = table.querySelector('thead');
        const tbody = table.querySelector('tbody');

        // 生成表头
        thead.innerHTML = `
            <tr>
                <th rowspan="2">区域</th>
                <th rowspan="2">泄漏量（SCFM）</th>
                <th colspan="${data.vehicles.length}">车型对比</th>
            </tr>
            <tr>
                ${data.vehicles.map(vehicle => `<th>${vehicle.name}</th>`).join('')}
            </tr>
        `;

        // 生成表格内容
        tbody.innerHTML = '';

        data.areas.forEach(area => {
            if (area.category === '整车不可控泄漏量') {
                // 整车不可控泄漏量单独一行
                const item = area.items[0];
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="category-cell">${area.category}</td>
                    <td></td>
                    ${item.values.map(value => `
                        <td class="value-cell ${value === null ? 'no-data' : ''}">${value !== null ? value : '-'}</td>
                    `).join('')}
                `;
                tbody.appendChild(row);
            } else {
                // 其他区域有子项
                area.items.forEach((item, index) => {
                    const row = document.createElement('tr');
                    if (index === 0) {
                        // 第一行显示分类名称
                        row.innerHTML = `
                            <td class="category-cell" rowspan="${area.items.length}">${area.category}</td>
                            <td class="subcategory-cell">${item.name}</td>
                            ${item.values.map(value => `
                                <td class="value-cell ${value === null ? 'no-data' : ''}">${value !== null ? value : '-'}</td>
                            `).join('')}
                        `;
                    } else {
                        // 后续行不显示分类名称
                        row.innerHTML = `
                            <td class="subcategory-cell">${item.name}</td>
                            ${item.values.map(value => `
                                <td class="value-cell ${value === null ? 'no-data' : ''}">${value !== null ? value : '-'}</td>
                            `).join('')}
                        `;
                    }
                    tbody.appendChild(row);
                });
            }
        });
    }

    renderTestInfoTable(table, data) {
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';

        data.vehicles.forEach(vehicle => {
            const row = document.createElement('tr');
            const testInfo = vehicle.test_info;
            row.innerHTML = `
                <td>${vehicle.name}</td>
                <td>${testInfo ? testInfo.test_date : '-'}</td>
                <td>${testInfo ? testInfo.test_engineer : '-'}</td>
                <td>${testInfo ? testInfo.test_location : '-'}</td>
            `;
            tbody.appendChild(row);
        });
    }

    async exportData() {
        const vehicleIds = this.multiSelect.getSelectedVehicleIds();
        if (vehicleIds.length === 0) {
            utils.showMessage('请先生成对比数据', 'warning');
            return;
        }

        try {
            const response = await fetch('/airtightness/api/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ vehicle_ids: vehicleIds })
            });

            if (!response.ok) {
                throw new Error('导出失败');
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'airtightness_comparison.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            utils.showMessage('数据导出成功', 'success');
        } catch (error) {
            utils.showMessage('导出数据失败: ' + error.message, 'error');
        }
    }

    showLoading(show) {
        const loading = document.getElementById('loading-indicator');
        loading.style.display = show ? 'block' : 'none';
    }

    showResults(show) {
        const resultsCard = document.getElementById('results-card');
        const emptyState = document.getElementById('empty-state');

        resultsCard.style.display = show ? 'block' : 'none';
        emptyState.style.display = show ? 'none' : 'block';

        if (show) {
            resultsCard.classList.add('fade-in');
        }
    }
}

// 图片查看管理器
class AirtightnessImageManager {
    constructor() {
        this.currentVehicleId = null;
        this.init();
    }

    init() {
        // 确保DOM元素存在后再初始化
        const select = document.getElementById('vehicle-select');
        if (!select) {
            console.error('vehicle-select 元素未找到，初始化失败');
            return;
        }

        this.loadVehicles();
        this.setupEventListeners();
    }

    async loadVehicles() {
        try {
            const result = await utils.request('/airtightness/api/all-vehicles');
            const select = document.getElementById('vehicle-select');

            if (!select) {
                console.error('vehicle-select 元素未找到，无法加载车型列表');
                return;
            }

            select.innerHTML = '<option value="">请选择车型...</option>';
            result.data.forEach(vehicle => {
                const option = document.createElement('option');
                option.value = vehicle.id;
                option.textContent = vehicle.name;
                select.appendChild(option);
            });
        } catch (error) {
            utils.showMessage('加载车型列表失败: ' + error.message, 'error');
        }
    }

    setupEventListeners() {
        const vehicleSelect = document.getElementById('vehicle-select');
        const viewImagesBtn = document.getElementById('view-images-btn');
        const openNewWindowBtn = document.getElementById('open-new-window-btn');

        // 检查所有必需元素是否存在
        if (!vehicleSelect || !viewImagesBtn || !openNewWindowBtn) {
            console.error('必需的DOM元素未找到，无法设置事件监听器');
            return;
        }

        // 车型选择变化
        vehicleSelect.addEventListener('change', (e) => {
            const vehicleId = e.target.value;
            this.currentVehicleId = vehicleId ? parseInt(vehicleId) : null;

            viewImagesBtn.disabled = !vehicleId;
            openNewWindowBtn.disabled = !vehicleId;
        });

        // 查看图片按钮
        viewImagesBtn.addEventListener('click', () => {
            if (this.currentVehicleId) {
                this.loadImages(this.currentVehicleId);
            }
        });

        // 新窗口打开按钮
        openNewWindowBtn.addEventListener('click', () => {
            if (this.currentVehicleId) {
                this.openInNewWindow(this.currentVehicleId);
            }
        });
    }

    async loadImages(vehicleId) {
        this.showLoading(true);

        try {
            const result = await utils.request(`/airtightness/api/images/${vehicleId}`);
            this.renderImages(result.data);
            this.showImages(true);
        } catch (error) {
            utils.showMessage('加载图片失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    renderImages(data) {
        const containers = {
            'front_compartment': document.getElementById('front-compartment-container'),
            'doors': document.getElementById('doors-container'),
            'tailgate': document.getElementById('tailgate-container')
        };

        Object.keys(containers).forEach(key => {
            const container = containers[key];
            const imagePath = data.images[key];

            if (imagePath) {
                container.innerHTML = `
                    <img src="/static/uploads/${imagePath}" alt="${this.getLocationName(key)}"
                         onclick="previewImage(this.src, '${data.vehicle.name} - ${this.getLocationName(key)}')">
                `;
            } else {
                container.innerHTML = `
                    <div class="no-image-placeholder">
                        <i class="fas fa-image fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无图片</p>
                    </div>
                `;
            }
        });
    }

    getLocationName(key) {
        const names = {
            'front_compartment': '前舱',
            'doors': '车门',
            'tailgate': '尾门'
        };
        return names[key] || key;
    }

    openInNewWindow(vehicleId) {
        const vehicleSelect = document.getElementById('vehicle-select');
        const vehicleName = vehicleSelect.options[vehicleSelect.selectedIndex].text;

        const newWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        newWindow.document.write(`
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${vehicleName} - 气密性测试图片</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                <style>
                    .image-container {
                        min-height: 300px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: #f8f9fa;
                        border-radius: 0.375rem;
                        overflow: hidden;
                    }
                    .image-container img {
                        max-width: 100%;
                        max-height: 400px;
                        object-fit: contain;
                        cursor: pointer;
                    }
                    .no-image-placeholder {
                        text-align: center;
                        color: #6c757d;
                    }
                </style>
            </head>
            <body>
                <div class="container-fluid py-4">
                    <h2 class="mb-4">${vehicleName} - 气密性测试图片</h2>
                    <div class="row" id="images-container">
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-car me-2"></i>前舱</h6>
                                </div>
                                <div class="card-body">
                                    <div class="image-container" id="front-compartment-container">
                                        <div class="spinner-border text-primary" role="status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-door-open me-2"></i>车门</h6>
                                </div>
                                <div class="card-body">
                                    <div class="image-container" id="doors-container">
                                        <div class="spinner-border text-primary" role="status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-car-side me-2"></i>尾门</h6>
                                </div>
                                <div class="card-body">
                                    <div class="image-container" id="tailgate-container">
                                        <div class="spinner-border text-primary" role="status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <script>
                    // 加载图片
                    fetch('/airtightness/api/images/${vehicleId}')
                        .then(response => response.json())
                        .then(result => {
                            if (result.code === 200) {
                                const data = result.data;
                                const containers = {
                                    'front_compartment': document.getElementById('front-compartment-container'),
                                    'doors': document.getElementById('doors-container'),
                                    'tailgate': document.getElementById('tailgate-container')
                                };

                                Object.keys(containers).forEach(key => {
                                    const container = containers[key];
                                    const imagePath = data.images[key];

                                    if (imagePath) {
                                        container.innerHTML = '<img src="/static/uploads/' + imagePath + '" alt="' + key + '">';
                                    } else {
                                        container.innerHTML = '<div class="no-image-placeholder"><i class="fas fa-image fa-3x text-muted mb-3"></i><p class="text-muted">暂无图片</p></div>';
                                    }
                                });
                            }
                        })
                        .catch(error => {
                            console.error('加载图片失败:', error);
                        });
                </script>
            </body>
            </html>
        `);

        newWindow.document.close();
    }

    showLoading(show) {
        const loading = document.getElementById('loading-indicator');
        loading.style.display = show ? 'block' : 'none';
    }

    showImages(show) {
        const imagesContainer = document.getElementById('images-container');
        const emptyState = document.getElementById('empty-state');

        imagesContainer.style.display = show ? 'flex' : 'none';
        emptyState.style.display = show ? 'none' : 'block';

        if (show) {
            imagesContainer.classList.add('fade-in');
        }
    }
}

// 图片预览功能
function previewImage(src, title) {
    const modal = document.getElementById('image-preview-modal');
    const modalTitle = document.getElementById('image-preview-title');
    const previewImage = document.getElementById('preview-image');

    modalTitle.textContent = title;
    previewImage.src = src;

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 初始化气密性页面的函数
function initAirtightnessPage() {
    // 添加更严格的DOM就绪检查
    if (document.readyState === 'loading') {
        // 如果DOM还在加载中，等待加载完成
        document.addEventListener('DOMContentLoaded', initAirtightnessPage);
        return;
    }

    // 检查是否存在气密性对比页面的元素
    const vehicleMultiselect = document.getElementById('vehicle-multiselect');
    const vehicleSelect = document.getElementById('vehicle-select');

    if (vehicleMultiselect) {
        // 气密性对比页面
        if (!window.airtightnessManager) {
            try {
                window.airtightnessManager = new AirtightnessComparisonManager();
                console.log('气密性对比页面初始化成功');
            } catch (error) {
                console.error('气密性对比页面初始化失败:', error);
                // 延迟重试
                setTimeout(() => {
                    if (!window.airtightnessManager) {
                        initAirtightnessPage();
                    }
                }, 100);
            }
        }
    }
    else if (vehicleSelect) {
        // 图片查看页面 - 添加更全面的DOM元素检查
        const viewImagesBtn = document.getElementById('view-images-btn');
        const openNewWindowBtn = document.getElementById('open-new-window-btn');
        const imagesContainer = document.getElementById('images-container');
        const emptyState = document.getElementById('empty-state');

        // 确保所有必需的DOM元素都存在
        if (viewImagesBtn && openNewWindowBtn && imagesContainer && emptyState) {
            if (!window.imageManager) {
                try {
                    window.imageManager = new AirtightnessImageManager();
                    console.log('气密性图片页面初始化成功');
                } catch (error) {
                    console.error('气密性图片页面初始化失败:', error);
                    // 延迟重试
                    setTimeout(() => {
                        if (!window.imageManager) {
                            initAirtightnessPage();
                        }
                    }, 100);
                }
            }
        } else {
            // DOM元素还未完全加载，延迟重试
            console.log('等待DOM元素加载完成...');
            setTimeout(initAirtightnessPage, 50);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;

    if (currentPath.includes('/airtightness/comparison')) {
        initAirtightnessPage();
    } else if (currentPath.includes('/airtightness/images')) {
        initAirtightnessPage();
    }
});
