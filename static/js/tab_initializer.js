/**
 * 标签页初始化管理器
 * 专门处理AJAX加载的标签页中JavaScript初始化时机问题
 */
class TabInitializer {
    constructor() {
        this.initQueue = new Map();
        this.scriptLoadStatus = new Map();
        this.maxRetries = 50;
        this.retryDelay = 100;
    }

    /**
     * 注册页面初始化任务
     * @param {string} pageType 页面类型
     * @param {HTMLElement} container 容器元素
     * @param {Array} requiredScripts 必需的脚本列表
     * @param {Function} initFunction 初始化函数
     */
    registerInitTask(pageType, container, requiredScripts, initFunction) {
        const taskId = `${pageType}_${Date.now()}`;
        
        this.initQueue.set(taskId, {
            pageType,
            container,
            requiredScripts,
            initFunction,
            retries: 0,
            status: 'pending'
        });

        console.log(`注册初始化任务: ${pageType}`);
        this.processInitTask(taskId);
    }

    /**
     * 处理初始化任务
     */
    async processInitTask(taskId) {
        const task = this.initQueue.get(taskId);
        if (!task || task.status === 'completed') {
            return;
        }

        // 检查DOM元素是否准备好
        if (!this.isDOMReady(task.container, task.pageType)) {
            if (task.retries < this.maxRetries) {
                task.retries++;
                setTimeout(() => this.processInitTask(taskId), this.retryDelay);
                return;
            } else {
                console.error(`DOM准备超时: ${task.pageType}`);
                task.status = 'failed';
                return;
            }
        }

        // 检查必需的脚本是否加载
        const scriptsReady = await this.checkScriptsReady(task.requiredScripts);
        if (!scriptsReady) {
            if (task.retries < this.maxRetries) {
                task.retries++;
                setTimeout(() => this.processInitTask(taskId), this.retryDelay);
                return;
            } else {
                console.error(`脚本加载超时: ${task.pageType}`);
                task.status = 'failed';
                return;
            }
        }

        // 执行初始化
        try {
            console.log(`执行初始化: ${task.pageType}`);
            await task.initFunction();
            task.status = 'completed';
            console.log(`初始化完成: ${task.pageType}`);
        } catch (error) {
            console.error(`初始化失败: ${task.pageType}`, error);
            task.status = 'failed';
        }
    }

    /**
     * 检查DOM是否准备好
     */
    isDOMReady(container, pageType) {
        switch (pageType) {
            case 'sound_insulation':
                return container.querySelector('#area-select') && 
                       container.querySelector('#vehicle-multiselect') &&
                       container.querySelector('#generate-comparison-btn');
            
            case 'airtightness_images':
                return container.querySelector('#vehicle-select') &&
                       container.querySelector('#view-images-btn') &&
                       container.querySelector('#open-new-window-btn') &&
                       container.querySelector('#images-container') &&
                       container.querySelector('#empty-state');
            
            case 'airtightness_comparison':
                return container.querySelector('#vehicle-multiselect') &&
                       container.querySelector('#generate-comparison-btn');
            
            default:
                return true;
        }
    }

    /**
     * 检查脚本是否准备好
     */
    async checkScriptsReady(requiredScripts) {
        for (const scriptInfo of requiredScripts) {
            const { globalVar, scriptUrl } = scriptInfo;
            
            // 检查全局变量是否存在
            if (globalVar && typeof window[globalVar] !== 'undefined') {
                continue;
            }
            
            // 检查脚本是否已加载
            if (scriptUrl && this.isScriptLoaded(scriptUrl)) {
                continue;
            }
            
            return false;
        }
        return true;
    }

    /**
     * 检查脚本是否已加载
     */
    isScriptLoaded(url) {
        const scripts = document.querySelectorAll('script[src]');
        for (const script of scripts) {
            if (script.src.includes(url)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 初始化隔声量页面
     */
    initSoundInsulationPage(container) {
        const requiredScripts = [
            { globalVar: 'SoundInsulationManager', scriptUrl: 'sound_insulation.js' }
        ];

        this.registerInitTask('sound_insulation', container, requiredScripts, () => {
            if (typeof initSoundInsulationPage === 'function') {
                return initSoundInsulationPage();
            } else {
                throw new Error('initSoundInsulationPage function not found');
            }
        });
    }

    /**
     * 初始化气密性图片页面
     */
    initAirtightnessImagesPage(container) {
        const requiredScripts = [
            { globalVar: 'AirtightnessImageManager', scriptUrl: 'airtightness.js' }
        ];

        this.registerInitTask('airtightness_images', container, requiredScripts, () => {
            if (typeof initAirtightnessPage === 'function') {
                return initAirtightnessPage();
            } else {
                throw new Error('initAirtightnessPage function not found');
            }
        });
    }

    /**
     * 初始化气密性对比页面
     */
    initAirtightnessComparisonPage(container) {
        const requiredScripts = [
            { globalVar: 'AirtightnessComparisonManager', scriptUrl: 'airtightness.js' }
        ];

        this.registerInitTask('airtightness_comparison', container, requiredScripts, () => {
            if (typeof initAirtightnessPage === 'function') {
                return initAirtightnessPage();
            } else {
                throw new Error('initAirtightnessPage function not found');
            }
        });
    }

    /**
     * 自动检测页面类型并初始化
     */
    autoInit(container) {
        // 检测隔声量页面
        if (container.querySelector('#area-select')) {
            this.initSoundInsulationPage(container);
            return;
        }

        // 检测气密性图片页面
        if (container.querySelector('#vehicle-select')) {
            this.initAirtightnessImagesPage(container);
            return;
        }

        // 检测气密性对比页面
        if (container.querySelector('#vehicle-multiselect')) {
            this.initAirtightnessComparisonPage(container);
            return;
        }

        console.log('未检测到需要初始化的页面类型');
    }
}

// 创建全局实例
window.tabInitializer = new TabInitializer();

// 导出便捷函数
window.initTabContent = function(container) {
    if (window.tabInitializer) {
        window.tabInitializer.autoInit(container);
    }
};
