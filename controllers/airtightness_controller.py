import csv
import io
from flask import Blueprint, request, render_template, make_response
from services.airtightness_service import AirtightnessService
from utils.result import success, error, bad_request
from decorators import login_required

airtightness_bp = Blueprint('airtightness', __name__, url_prefix='/airtightness')
airtightness_service = AirtightnessService()

class AirtightnessController:
    """气密性控制器"""
    
    @staticmethod
    @airtightness_bp.route('/comparison')
    @login_required
    def comparison_page():
        """泄漏量对比页面 - 标签页内容"""
        return render_template('airtightness/airtightness_comparison_content.html')
    
    @staticmethod
    @airtightness_bp.route('/images')
    @login_required
    def images_page():
        """测试图片查看页面 - 标签页内容"""
        return render_template('airtightness/airtightness_images_content.html')
    
    @staticmethod
    @airtightness_bp.route('/api/vehicles')
    @login_required
    def get_vehicles():
        """获取有气密性测试数据的车型列表"""
        try:
            vehicles = airtightness_service.get_vehicle_list()
            return success(vehicles)
        except Exception as e:
            return error(f"获取车型列表失败: {str(e)}")
    
    @staticmethod
    @airtightness_bp.route('/api/all-vehicles')
    @login_required
    def get_all_vehicles():
        """获取所有车型列表（用于图片查看）"""
        try:
            vehicles = airtightness_service.get_all_vehicles()
            return success(vehicles)
        except Exception as e:
            return error(f"获取车型列表失败: {str(e)}")
    
    @staticmethod
    @airtightness_bp.route('/api/comparison', methods=['POST'])
    @login_required
    def generate_comparison():
        """生成泄漏量对比数据"""
        try:
            data = request.get_json()
            vehicle_ids = data.get('vehicle_ids', [])
            
            if not vehicle_ids:
                return bad_request("请选择至少一个车型")
            
            # 转换为整数列表
            vehicle_ids = [int(vid) for vid in vehicle_ids]
            
            comparison_data = airtightness_service.generate_comparison_data(vehicle_ids)
            return success(comparison_data, "对比数据生成成功")
        except ValueError:
            return bad_request("车型ID格式错误")
        except Exception as e:
            return error(f"生成对比数据失败: {str(e)}")
    
    @staticmethod
    @airtightness_bp.route('/api/images/<int:vehicle_id>')
    @login_required
    def get_vehicle_images(vehicle_id):
        """获取指定车型的测试图片"""
        try:
            images_data = airtightness_service.get_vehicle_images(vehicle_id)
            if not images_data:
                return error("车型不存在", 404)
            return success(images_data)
        except Exception as e:
            return error(f"获取测试图片失败: {str(e)}")
    
    @staticmethod
    @airtightness_bp.route('/api/export', methods=['POST'])
    @login_required
    def export_comparison():
        """导出对比数据"""
        try:
            data = request.get_json()
            vehicle_ids = data.get('vehicle_ids', [])
            
            if not vehicle_ids:
                return bad_request("请选择至少一个车型")
            
            # 转换为整数列表
            vehicle_ids = [int(vid) for vid in vehicle_ids]
            
            csv_data = airtightness_service.export_comparison_data(vehicle_ids)
            if not csv_data:
                return error("没有可导出的数据")
            
            # 创建CSV文件
            output = io.StringIO()
            writer = csv.writer(output)
            for row in csv_data:
                writer.writerow(row)
            
            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = 'attachment; filename=airtightness_comparison.csv'
            
            return response
        except ValueError:
            return bad_request("车型ID格式错误")
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")
