#!/usr/bin/env python3
"""
测试气密性图片页面初始化修复
"""

import requests
import json
import time

def test_airtightness_api():
    """测试气密性API是否正常工作"""
    base_url = "http://127.0.0.1:5000"
    
    print("测试气密性API...")
    
    # 测试获取所有车型列表
    try:
        response = requests.get(f"{base_url}/airtightness/api/all-vehicles")
        print(f"获取车型列表状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    
    # 测试获取页面内容
    try:
        response = requests.get(f"{base_url}/airtightness/images", 
                              headers={'X-Requested-With': 'XMLHttpRequest'})
        print(f"\n获取页面内容状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("页面内容获取成功")
            # 检查关键元素是否存在
            content = response.text
            if 'vehicle-select' in content:
                print("✓ vehicle-select 元素存在")
            if 'view-images-btn' in content:
                print("✓ view-images-btn 元素存在")
            if 'open-new-window-btn' in content:
                print("✓ open-new-window-btn 元素存在")
            if 'images-container' in content:
                print("✓ images-container 元素存在")
            if 'empty-state' in content:
                print("✓ empty-state 元素存在")
        else:
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_airtightness_api()
